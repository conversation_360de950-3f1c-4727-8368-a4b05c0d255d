name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main, '**' ]

jobs:
  lint:
    name: <PERSON> Linting
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.22.5'

    - name: Run Go Linting
      uses: golangci/golangci-lint-action@v4
      with:
        version: latest
        skip-cache: true
        args: --out-format=github-actions --timeout=5m
  
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.22.5'

    - name: Set up Node
      uses: actions/setup-node@v4
      with:
        node-version: '>=20.15.0'

    - name: Run unit tests
      run: make test > test_output.txt

    - name: Generate test coverage report
      run: go tool cover -html=./cover.out -o ./cover.html

    - name: Upload coverage report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: ./cover.html

    - name: Upload test_output report
      uses: actions/upload-artifact@v4
      with:
        name: test_output
        path: ./test_output.txt

  build:
    runs-on: ubuntu-latest
    needs: 
      - test
      - lint

    steps:
    - name: Check out the repository
      uses: actions/checkout@v4

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_CI_ACCESS_TOKEN }}

    - name: Extract branch name
      id: extract_branch_name
      shell: bash
      run: |
        echo "digest=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT

    - name: Extract image name
      id: extract_image_name
      shell: bash
      run: |
        if [[ $GITHUB_REF_NAME == "main" ]]; then
          echo "digest=main-${GITHUB_SHA}" >> $GITHUB_OUTPUT
        else          
          echo "digest=${{ steps.extract_branch_name.outputs.digest }}-${GITHUB_SHA}" >> $GITHUB_OUTPUT
        fi

    - name: Log in to DigitalOcean Container Registry with short-lived credentials
      run: doctl registry login --expiry-seconds 1200
  
    - name: Build container cron image
      run: docker build -t registry.digitalocean.com/aaronromeo/postmanpat/cron:${{ steps.extract_image_name.outputs.digest }} -f Dockerfile.cron .

    - name: Clean up old cron images
      run: |
        set -euxo pipefail

        branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}
        echo "Branch: ${branch}"
        doctl registry repository list-tags postmanpat/cron --output json | jq -r '.[] | select(.tag | contains("'${branch}'")) | .manifest_digest' | xargs -I {} doctl registry repository delete-manifest postmanpat/cron --force {}

    - name: Push cron image to DigitalOcean Container Registry
      run: docker push registry.digitalocean.com/aaronromeo/postmanpat/cron:${{ steps.extract_image_name.outputs.digest }}

    - name: Tag and push latest cron image to DigitalOcean Container Registry
      run: |
        if [[ $GITHUB_REF_NAME == "main" ]]; then
          docker tag registry.digitalocean.com/aaronromeo/postmanpat/cron:${{ steps.extract_image_name.outputs.digest }} registry.digitalocean.com/aaronromeo/postmanpat/cron:latest
          docker push registry.digitalocean.com/aaronromeo/postmanpat/cron:latest
        else
          docker tag registry.digitalocean.com/aaronromeo/postmanpat/cron:${{ steps.extract_image_name.outputs.digest }} registry.digitalocean.com/aaronromeo/postmanpat/cron:${{ steps.extract_branch_name.outputs.digest }}
          docker push registry.digitalocean.com/aaronromeo/postmanpat/cron:${{ steps.extract_branch_name.outputs.digest }}
        fi

    - name: Build container ws image
      run: docker build -t registry.digitalocean.com/aaronromeo/postmanpat/ws:${{ steps.extract_image_name.outputs.digest }} -f Dockerfile.ws .

    - name: Clean up old ws images
      run: |
        set -euxo pipefail

        branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}
        echo "Branch: ${branch}"
        doctl registry repository list-tags postmanpat/ws --output json | jq -r '.[] | select(.tag | contains("'${branch}'")) | .manifest_digest' | xargs -I {} doctl registry repository delete-manifest postmanpat/ws --force {}

    - name: Push ws image to DigitalOcean Container Registry
      run: docker push registry.digitalocean.com/aaronromeo/postmanpat/ws:${{ steps.extract_image_name.outputs.digest }}

    - name: Tag and push latest ws image to DigitalOcean Container Registry
      run: |
        if [[ $GITHUB_REF_NAME == "main" ]]; then
          docker tag registry.digitalocean.com/aaronromeo/postmanpat/ws:${{ steps.extract_image_name.outputs.digest }} registry.digitalocean.com/aaronromeo/postmanpat/ws:latest
          docker push registry.digitalocean.com/aaronromeo/postmanpat/ws:latest
        else
          docker tag registry.digitalocean.com/aaronromeo/postmanpat/ws:${{ steps.extract_image_name.outputs.digest }} registry.digitalocean.com/aaronromeo/postmanpat/ws:${{ steps.extract_branch_name.outputs.digest }}
          docker push registry.digitalocean.com/aaronromeo/postmanpat/ws:${{ steps.extract_branch_name.outputs.digest }}
        fi

  deploy:
    runs-on: ubuntu-latest
    needs: build

    steps:
    - name: Check out the repository
      uses: actions/checkout@v4

    # - name: Set up SSH
    #   run: |
    #     mkdir -p ~/.ssh
    #     echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
    #     chmod 600 ~/.ssh/id_rsa

    # - name: Deploy to Production
    #   if: github.ref_name == 'main'
    #   run: |
    #     if [[ "${{ secrets.PROD_DROPLET_URL }}" == "" ]]; then
    #       echo "No production droplet URL provided. Skipping deployment."
    #       exit 0
    #     fi

    # Note: The following code is commented out because the image uses `containrrr/watchtower` 
    #       to automatically update the container when a new image is pushed to the registry.
    # - name: Deploy to Production
    #   if: github.ref_name == 'main'
    #   uses: joelwmale/webhook-action@master
    #   with:
    #     url: ${{ secrets.PROD_DROPLET_URL }}

    #     if [[ "${{ secrets.PROD_DROPLET_IP }}" == "" ]]; then
    #       echo "No production droplet IP provided. Skipping deployment."
    #       exit 0
    #     fi

    #     ssh -o StrictHostKeyChecking=no root@${{ secrets.PROD_DROPLET_IP }} << 'EOF'
    #       echo "${{ secrets.PROD_ENV_FILE }}" > .env.prod
    #       doctl registry login --expiry-seconds 1200
    #       docker pull registry.digitalocean.com/aaronromeo/postmanpat:main
    #       docker stop postmanpat-prod || true
    #       docker rm postmanpat-prod || true
    #       docker run -d --env-file .env.prod -p 80:8080 --name postmanpat-prod registry.digitalocean.com/aaronromeo/postmanpat:main
    #     EOF

    # - name: Deploy to Staging
    #   if: github.ref_name != 'main'
    #   run: |
    #     if [[ "${{ secrets.STAGING_DROPLET_IP }}" == "" ]]; then
    #       echo "No staging droplet IP provided. Skipping deployment."
    #       exit 0
    #     fi

    #     ssh -o StrictHostKeyChecking=no root@${{ secrets.STAGING_DROPLET_IP }} << 'EOF'
    #       echo "${{ secrets.STAGING_ENV_FILE }}" > .env.staging
    #       doctl registry login --expiry-seconds 1200
    #       docker pull registry.digitalocean.com/aaronromeo/postmanpat:${{ steps.extract_image_name.outputs.digest }}
    #       docker stop postmanpat-staging || true
    #       docker rm postmanpat-staging || true
    #       docker run -d --env-file .env.staging -p 80:8080 --name postmanpat-staging registry.digitalocean.com/aaronromeo/postmanpat:${{ steps.extract_image_name.outputs.digest }}
    #     EOF

  cleanup:
    runs-on: ubuntu-latest
    needs: deploy

    steps:
    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_CI_ACCESS_TOKEN }}

    - name: Log in to DigitalOcean Container Registry with short-lived credentials
      run: doctl registry login --expiry-seconds 1200
    
    - name: Run garbage collection
      if: github.ref_name == 'main'
      run: |
        doctl registry garbage-collection start --force

