locals {
  # Use PUBLIC_KEY_SHA if provided, otherwise read from file
  ssh_public_key = var.PUBLIC_KEY_SHA != "" ? var.PUBLIC_KEY_SHA : file(var.PUBLIC_KEY_FILE)
  
  # Extract the key name from the public key (assuming standard format)
  key_name_parts = split(" ", local.ssh_public_key)
  key_name = length(local.key_name_parts) > 2 ? element(local.key_name_parts, 2) : "Terraform Dokku"
}

# Check if the SSH key already exists by name
data "digitalocean_ssh_keys" "existing" {}

# We don't create the SSH key anymore, but we still need to find it
locals {
  # Try to find the fingerprint of an existing key with the same name
  matching_keys = [for key in data.digitalocean_ssh_keys.existing.ssh_keys : key.fingerprint if key.name == local.key_name]
  existing_key_fingerprint = length(local.matching_keys) > 0 ? local.matching_keys[0] : ""
  
  # Combine the existing key fingerprint with any additional fingerprints
  # Only add the fingerprint if it's not empty and not already in the list
  should_add_fingerprint = local.existing_key_fingerprint != "" && !contains(var.SSH_FINGERPRINTS, local.existing_key_fingerprint)
  all_ssh_keys = local.should_add_fingerprint ? concat([local.existing_key_fingerprint], var.SSH_FINGERPRINTS) : var.SSH_FINGERPRINTS
}

# Move common infrastructure code here
resource "digitalocean_droplet" "dokku" {
  image              = "ubuntu-22-04-x64"
  ipv6               = false
  monitoring         = true
  name               = "dokku-pack-${var.environment}"
  region             = var.region
  size               = var.droplet_size
  ssh_keys           = local.all_ssh_keys
  user_data          = templatefile("${path.module}/install-dokku.sh.tpl", { hostname = var.dokku_hostname })

  tags = ["dokku", var.environment]
}

output "droplet_ip" {
  value = digitalocean_droplet.dokku.ipv4_address
}
