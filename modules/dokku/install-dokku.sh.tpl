#!/bin/bash

# Exit immediately if a command exits with a non-zero status
set -e

# Function to log and exit on error
error_exit() {
  echo "ERROR: $1" >&2
  echo "INSTALLATION FAILED" > /var/log/dokku-install-status.log
  exit 1
}

# Log start of installation
echo "Starting Dokku installation..." > /var/log/dokku-install-status.log

# Add 2G of swap memory
echo "Setting up swap space..." >> /var/log/dokku-install-status.log
fallocate -l 2G /swapfile || error_exit "Failed to allocate swap file"
chmod 600 /swapfile || error_exit "Failed to set permissions on swap file"
mkswap /swapfile || error_exit "Failed to set up swap file"
swapon /swapfile || error_exit "Failed to enable swap file"

# install docker
echo "Installing Docker..." >> /var/log/dokku-install-status.log
wget -nv -O - https://get.docker.com/ | sh || error_exit "Docker installation failed"

# setup dokku apt repository
echo "Setting up Dokku repository..." >> /var/log/dokku-install-status.log
curl -fsSL https://packagecloud.io/dokku/dokku/gpgkey | sudo gpg --dearmor -o /etc/apt/keyrings/dokku.gpg || error_exit "Failed to download Dokku GPG key"
chmod a+r /etc/apt/keyrings/dokku.gpg || error_exit "Failed to set permissions on Dokku GPG key"

# Detect OS version
export OS_ID="$(lsb_release -cs 2>/dev/null || echo "jammy")"
echo "Detected OS: $OS_ID" >> /var/log/dokku-install-status.log

# Add repository with the new method for Ubuntu 22.04
echo "deb [signed-by=/etc/apt/keyrings/dokku.gpg] https://packagecloud.io/dokku/dokku/ubuntu/ $OS_ID main" | sudo tee /etc/apt/sources.list.d/dokku.list || error_exit "Failed to add Dokku repository"

apt-get update || error_exit "Failed to update package lists"

# set options for non-interactive install
echo "Configuring Dokku installation options..." >> /var/log/dokku-install-status.log
echo "dokku dokku/web_config boolean false" | debconf-set-selections
echo "dokku dokku/vhost_enable boolean true" | debconf-set-selections
echo "dokku dokku/nginx_enable boolean true" | debconf-set-selections
echo "dokku dokku/skip_key_file boolean true" | debconf-set-selections
echo "dokku dokku/key_file string /root/.ssh/authorized_keys" | debconf-set-selections
echo "dokku dokku/hostname string ${hostname}" | debconf-set-selections

# install dokku
echo "Installing Dokku..." >> /var/log/dokku-install-status.log
apt-get install -y dokku || error_exit "Dokku installation failed"

# Verify dokku is installed
if ! command -v dokku &> /dev/null; then
  error_exit "Dokku command not found after installation"
fi

# Install core dependencies
echo "Installing Dokku dependencies..." >> /var/log/dokku-install-status.log
dokku plugin:install-dependencies --core || error_exit "Failed to install Dokku dependencies"

# add plugins
echo "Installing Dokku plugins..." >> /var/log/dokku-install-status.log
dokku plugin:install https://github.com/dokku/dokku-postgres.git || error_exit "Failed to install Postgres plugin"
dokku plugin:install https://github.com/dokku/dokku-letsencrypt.git || error_exit "Failed to install Let's Encrypt plugin"
dokku letsencrypt:cron-job --add || error_exit "Failed to set up Let's Encrypt cron job"

# Make sure the dokku user and .ssh directory exist
echo "Setting up SSH access..." >> /var/log/dokku-install-status.log
mkdir -p /home/<USER>/.ssh || error_exit "Failed to create Dokku SSH directory"

# Enable ssh access for the `dokku` user
cat /root/.ssh/authorized_keys >> /home/<USER>/.ssh/authorized_keys || error_exit "Failed to copy SSH keys"
chown -R dokku:dokku /home/<USER>/.ssh || error_exit "Failed to set ownership on Dokku SSH directory"
chmod 700 /home/<USER>/.ssh || error_exit "Failed to set permissions on Dokku SSH directory"
chmod 600 /home/<USER>/.ssh/authorized_keys || error_exit "Failed to set permissions on Dokku authorized_keys"

# Set global domain
echo "Setting up Dokku domain..." >> /var/log/dokku-install-status.log
dokku domains:set-global ${hostname} || error_exit "Failed to set Dokku global domain"

# Log successful completion
echo "Dokku installation completed successfully" > /var/log/dokku-install-status.log
echo "INSTALLATION SUCCESSFUL" >> /var/log/dokku-install-status.log
