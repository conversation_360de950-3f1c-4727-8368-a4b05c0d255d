variable "do_api_token" {
  description = "DigitalOcean API token"
  type        = string
}

variable "PUBLIC_KEY_FILE" {
  description = "Path to the public key file"
  type        = string
  default     = "~/.ssh/terraform_dokku.pub"
}

variable "SSH_FINGERPRINTS" {
  description = "SSH key fingerprints"
  type        = list(string)
  default     = []
}

variable "region" {
  description = "Digitalocean region"
  type        = string
  default     = "nyc3"
}


variable "environment" {
  description = "Environment name (production or testing)"
  type        = string
}

variable "droplet_size" {
  description = "Size of the droplet"
  type        = string
}

variable "dokku_hostname" {
  description = "Hostname for the Dokku instance"
  type        = string
  default     = "overachieverlabs.com"
}

variable "PUBLIC_KEY_SHA" {
  description = "SSH public key content (if provided, this will be used instead of reading from PUBLIC_KEY_FILE)"
  type        = string
  default     = ""
}
