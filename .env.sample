# PostmanPat Environment Configuration
# Copy this file to .env and fill in your values for local development

# IMAP Configuration
IMAP_URL="imap.gmail.com:993"
IMAP_USER="<EMAIL>"
IMAP_PASS="your-app-password"

# DigitalOcean Spaces Configuration
DIGITALOCEAN_BUCKET_ACCESS_KEY="your-spaces-access-key"
DIGITALOCEAN_BUCKET_SECRET_KEY="your-spaces-secret-key"

# Monitoring Configuration
UPTRACE_DSN="https://<EMAIL>/your-project-id"

# Note: For production deployment, these values should be configured as GitHub Secrets
# See DEPLOYMENT.md for more information

# Optional: Enable Let's Encrypt SSL certificates (true/false)
ENABLE_LETSENCRYPT=true
