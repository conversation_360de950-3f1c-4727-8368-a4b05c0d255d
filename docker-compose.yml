# Local development docker-compose configuration
# For production deployment, see .github/workflows/deploy.yml

services:
  cron:
    build:
      context: .
      dockerfile: Dockerfile.cron
    env_file: ".env"
    container_name: cron-container
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: 3
  webserver:
    build:
      context: .
      dockerfile: Dockerfile.ws
    ports:
      - "3000:3000" # Access http://localhost:3000
    env_file: ".env"
    container_name: web-container
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: 3

